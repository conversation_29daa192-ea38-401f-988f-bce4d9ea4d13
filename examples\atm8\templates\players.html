{% extends "base.html" %}

{% block title %}Players - Minecraft Server Manager{% endblock %}

{% block content %}
<div class="row g-4">
    <div class="col-lg-8">
        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Online Players
                </h5>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-primary fs-6 animate__animated animate__pulse animate__infinite">
                        {{ online_players.online_count }}/{{ online_players.max_players }}
                    </span>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshPlayers()" data-bs-toggle="tooltip" title="Refresh players">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if detailed_players %}
                <div class="row g-3">
                    {% for player in detailed_players %}
                    <div class="col-md-6">
                        <div class="card player-card border-0 shadow-sm animate__animated animate__fadeInUp" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                            <i class="fas fa-user text-success"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1 fw-bold">{{ player.name }}</h6>
                                        <span class="badge bg-success-subtle text-success">
                                            <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>Online
                                        </span>
                                    </div>
                                </div>

                                <div class="row g-2 mb-3">
                                    {% if player.position %}
                                    <div class="col-12">
                                        <div class="d-flex align-items-center text-muted">
                                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                            <small>
                                                <strong>Position:</strong>
                                                {{ "%.1f"|format(player.position.x) }},
                                                {{ "%.1f"|format(player.position.y) }},
                                                {{ "%.1f"|format(player.position.z) }}
                                            </small>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if player.health %}
                                    <div class="col-6">
                                        <div class="d-flex align-items-center text-muted">
                                            <i class="fas fa-heart me-2 text-danger"></i>
                                            <small><strong>Health:</strong> {{ player.health }}/20</small>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if player.gamemode is defined %}
                                    <div class="col-6">
                                        <div class="d-flex align-items-center text-muted">
                                            <i class="fas fa-gamepad me-2 text-info"></i>
                                            <small><strong>Mode:</strong> {{ {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}.get(player.gamemode, "Unknown") }}</small>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="d-flex gap-1 flex-wrap">
                                    <button type="button" class="btn btn-sm btn-outline-warning" data-bs-toggle="modal" data-bs-target="#kickModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Kick player">
                                        <i class="fas fa-sign-out-alt"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#banModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Ban player">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#teleportModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Teleport player">
                                        <i class="fas fa-location-arrow"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#opModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Give operator status">
                                        <i class="fas fa-crown"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sendPrivateMessage('{{ player.name }}')" data-bs-toggle="tooltip" title="Send private message">
                                        <i class="fas fa-comment"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <div class="animate__animated animate__fadeIn">
                        <i class="fas fa-user-slash fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted mb-2">No Players Online</h5>
                        <p class="text-muted">Players will appear here when they join the server</p>
                        <button class="btn btn-outline-primary" onclick="refreshPlayers()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card animate__animated animate__fadeInRight">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Whitelist
                </h5>
                <div class="d-flex align-items-center gap-2">
                    {% if whitelist %}
                    <span class="badge bg-success fs-6">
                        {{ whitelist|length }}
                    </span>
                    {% endif %}
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshWhitelist()" data-bs-toggle="tooltip" title="Refresh whitelist">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addWhitelistModal" data-bs-toggle="tooltip" title="Add player to whitelist">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                {% if whitelist %}
                    {% for player in whitelist %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-success bg-opacity-10 rounded animate__animated animate__fadeIn border border-success border-opacity-25" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-check text-success me-2"></i>
                            <span class="fw-medium">{{ player }}</span>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="confirmRemoveWhitelist('{{ player }}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-list fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">No whitelisted players</p>
                    <small class="text-muted">Whitelist is currently disabled</small>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4 animate__animated animate__fadeInRight animate__delay-1s">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ban me-2"></i>Banned Players
                </h5>
                <div class="d-flex align-items-center gap-2">
                    {% if banlist %}
                    <span class="badge bg-danger fs-6">
                        {{ banlist|length }}
                    </span>
                    {% endif %}
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshBanlist()" data-bs-toggle="tooltip" title="Refresh ban list">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="max-height: 250px; overflow-y: auto;">
                {% if banlist %}
                    {% for player in banlist %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-3 bg-danger bg-opacity-10 rounded animate__animated animate__fadeIn border border-danger border-opacity-25" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="flex-shrink-0">
                                <div class="bg-danger bg-opacity-20 rounded-circle p-2">
                                    <i class="fas fa-user-slash text-danger"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 fw-bold text-danger">{{ player }}</h6>
                                <span class="badge bg-danger-subtle text-danger">
                                    <i class="fas fa-ban me-1" style="font-size: 0.6rem;"></i>Banned
                                </span>
                            </div>
                        </div>
                        <div class="d-flex gap-1">
                            <button type="button" class="btn btn-sm btn-outline-success"
                                    onclick="confirmUnban('{{ player }}')"
                                    title="Unban player">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info"
                                    onclick="showBanInfo('{{ player }}')"
                                    data-bs-toggle="tooltip"
                                    title="View ban details">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <div class="animate__animated animate__fadeIn">
                        <i class="fas fa-shield-alt fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted mb-2">No Banned Players</h5>
                        <p class="text-muted">Server is clean! No players are currently banned.</p>
                        <button class="btn btn-outline-primary" onclick="refreshBanlist()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4 animate__animated animate__fadeInRight animate__delay-2s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <button class="btn btn-outline-warning d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#kickModal">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Kick Player</div>
                            <small class="text-muted">Remove player temporarily</small>
                        </div>
                    </button>
                    <button class="btn btn-outline-danger d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#banModal">
                        <i class="fas fa-ban me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Ban Player</div>
                            <small class="text-muted">Permanently ban player</small>
                        </div>
                    </button>
                    <button class="btn btn-outline-success d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#opModal">
                        <i class="fas fa-crown me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Give Operator</div>
                            <small class="text-muted">Grant admin privileges</small>
                        </div>
                    </button>
                    <button class="btn btn-outline-info d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#teleportModal">
                        <i class="fas fa-location-arrow me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Teleport Player</div>
                            <small class="text-muted">Move player to location</small>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Kick Modal -->
<div class="modal fade" id="kickModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-warning bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-sign-out-alt text-warning me-2"></i>
                    Kick Player
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('kick_player') }}" class="modal-form" data-success-message="Player kicked successfully!">
                <div class="modal-body p-4">
                    <div class="alert alert-warning border-0 bg-warning bg-opacity-10">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This will temporarily remove the player from the server.
                    </div>
                    <div class="mb-3">
                        <label for="kickPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="kickPlayerName" name="player_name" placeholder="Enter player name" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="kickReason" class="form-label fw-medium">Reason</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-comment"></i>
                            </span>
                            <input type="text" class="form-control" id="kickReason" name="reason" value="Kicked by admin" placeholder="Enter kick reason">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-sign-out-alt me-2"></i>Kick Player
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Ban Modal -->
<div class="modal fade" id="banModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-ban text-danger me-2"></i>
                    Ban Player
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('ban_player') }}" class="modal-form" data-success-message="Player banned successfully!">
                <div class="modal-body p-4">
                    <div class="alert alert-danger border-0 bg-danger bg-opacity-10">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will permanently ban the player from the server.
                    </div>
                    <div class="mb-3">
                        <label for="banPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="banPlayerName" name="player_name" placeholder="Enter player name" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="banReason" class="form-label fw-medium">Reason</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-comment"></i>
                            </span>
                            <input type="text" class="form-control" id="banReason" name="reason" value="Banned by admin" placeholder="Enter ban reason">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-ban me-2"></i>Ban Player
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Teleport Modal -->
<div class="modal fade" id="teleportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Teleport Player</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('teleport_player') }}" class="modal-form" data-success-message="Player teleported successfully!">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="tpPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="tpPlayerName" name="player_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="tpTarget" class="form-label">Target (player name or coordinates)</label>
                        <input type="text" class="form-control" id="tpTarget" name="target" placeholder="PlayerName or 0 64 0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Teleport</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- OP Modal -->
<div class="modal fade" id="opModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Give Operator Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('op_player') }}" class="modal-form" data-success-message="Player given operator status!">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="opPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="opPlayerName" name="player_name" required>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Operator status gives players full administrative privileges.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Give OP</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

<!-- Add Whitelist Modal -->
<div class="modal fade" id="addWhitelistModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-success bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-user-plus text-success me-2"></i>
                    Add to Whitelist
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_whitelist') }}" class="modal-form" data-success-message="Player added to whitelist!">
                <div class="modal-body p-4">
                    <div class="mb-3">
                        <label for="whitelistPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="whitelistPlayerName" name="player_name" placeholder="Enter player name" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-user-plus me-2"></i>Add to Whitelist
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Unban Modal -->
<div class="modal fade" id="unbanModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-success bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-undo text-success me-2"></i>
                    Unban Player
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('unban_player') }}" class="modal-form" data-success-message="Player unbanned successfully!">
                <div class="modal-body p-4">
                    <div class="alert alert-success border-0 bg-success bg-opacity-10">
                        <i class="fas fa-info-circle me-2"></i>
                        This will remove the player from the ban list and allow them to rejoin the server.
                    </div>
                    <div class="mb-3">
                        <label for="unbanPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="unbanPlayerName" name="player_name" placeholder="Enter player name" required readonly>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-lightbulb me-1"></i>
                            The player will be able to join the server immediately after unbanning.
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-undo me-2"></i>Unban Player
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block scripts %}

<script>
function setPlayerName(playerName) {
    document.getElementById('kickPlayerName').value = playerName;
    document.getElementById('banPlayerName').value = playerName;
    document.getElementById('tpPlayerName').value = playerName;
    document.getElementById('opPlayerName').value = playerName;
}

function confirmUnban(playerName) {
    Swal.fire({
        title: 'Unban Player?',
        html: `
            <div class="text-start">
                <p><strong>Player:</strong> ${playerName}</p>
                <p class="text-muted">This will remove the player from the ban list and allow them to rejoin the server.</p>
            </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#22c55e',
        cancelButtonColor: '#6b7280',
        confirmButtonText: '<i class="fas fa-undo me-2"></i>Yes, unban!',
        cancelButtonText: 'Cancel',
        customClass: {
            popup: 'text-start'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Unbanning Player...',
                text: 'Please wait while we process your request.',
                icon: 'info',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Perform the unban
            fetch('/action/unban', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: 'player_name=' + encodeURIComponent(playerName)
            })
            .then(response => {
                console.log('Unban response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Unban response data:', data);
                Swal.fire({
                    title: 'Success!',
                    text: data.message || `${playerName} has been unbanned successfully!`,
                    icon: 'success',
                    confirmButtonColor: '#22c55e'
                }).then(() => {
                    // Refresh the page to show updated ban list
                    window.location.reload();
                });
            })
            .catch(error => {
                console.error('Unban error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: error.message || 'An unexpected error occurred. Please try again.',
                    icon: 'error',
                    confirmButtonColor: '#ef4444'
                });
            });
        }
    });
}



function refreshPlayers() {
    const btn = event.target.closest('button');
    const icon = btn.querySelector('i');
    icon.classList.add('fa-spin');

    // Fetch updated detailed player data in a single call
    fetch('/api/players/detailed')
        .then(response => response.json())
        .then(data => {
            // Update player count badge
            const badge = document.querySelector('.badge');
            if (badge && data.online_count !== undefined) {
                badge.textContent = `${data.online_count}/${data.max_players}`;
            }

            // Update the player list with detailed data
            updatePlayerList(data.detailed_players || []);
            ToastManager.show('Player data refreshed!', 'success');
        })
        .catch(error => {
            console.error('Error refreshing players:', error);
            ToastManager.show('Failed to refresh player data', 'error');
        })
        .finally(() => {
            icon.classList.remove('fa-spin');
        });
}

function updatePlayerList(detailedPlayers) {
    const cardBody = document.querySelector('.card-body');
    if (!cardBody) return;

    if (detailedPlayers.length === 0) {
        // Show "No Players Online" message
        cardBody.innerHTML = `
            <div class="text-center py-5">
                <div class="animate__animated animate__fadeIn">
                    <i class="fas fa-user-slash fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted mb-2">No Players Online</h5>
                    <p class="text-muted">Players will appear here when they join the server</p>
                    <button class="btn btn-outline-primary" onclick="refreshPlayers()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
        `;
    } else {
        // Create player cards
        const playersHtml = detailedPlayers.map((player, index) => {
            const positionHtml = player.position ? `
                <div class="col-12">
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        <small>
                            <strong>Position:</strong>
                            ${player.position.x.toFixed(1)},
                            ${player.position.y.toFixed(1)},
                            ${player.position.z.toFixed(1)}
                        </small>
                    </div>
                </div>
            ` : '';

            const healthHtml = player.health !== undefined ? `
                <div class="col-6">
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-heart me-2 text-danger"></i>
                        <small><strong>Health:</strong> ${player.health}/20</small>
                    </div>
                </div>
            ` : '';

            const gamemodeHtml = player.gamemode !== undefined ? `
                <div class="col-6">
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-gamepad me-2 text-info"></i>
                        <small><strong>Mode:</strong> ${
                            {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}[player.gamemode] || "Unknown"
                        }</small>
                    </div>
                </div>
            ` : '';

            return `
                <div class="col-md-6">
                    <div class="card player-card border-0 shadow-sm animate__animated animate__fadeInUp" style="animation-delay: ${index * 0.1}s;">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                        <i class="fas fa-user text-success"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1 fw-bold">${player.name}</h6>
                                    <span class="badge bg-success-subtle text-success">
                                        <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>Online
                                    </span>
                                </div>
                            </div>

                            <div class="row g-2 mb-3">
                                ${positionHtml}
                                ${healthHtml}
                                ${gamemodeHtml}
                            </div>

                            <div class="d-flex gap-1 flex-wrap">
                                <button type="button" class="btn btn-sm btn-outline-warning" data-bs-toggle="modal" data-bs-target="#kickModal" onclick="setPlayerName('${player.name}')" data-bs-toggle="tooltip" title="Kick player">
                                    <i class="fas fa-sign-out-alt"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#banModal" onclick="setPlayerName('${player.name}')" data-bs-toggle="tooltip" title="Ban player">
                                    <i class="fas fa-ban"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#teleportModal" onclick="setPlayerName('${player.name}')" data-bs-toggle="tooltip" title="Teleport player">
                                    <i class="fas fa-location-arrow"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#opModal" onclick="setPlayerName('${player.name}')" data-bs-toggle="tooltip" title="Give operator status">
                                    <i class="fas fa-crown"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sendPrivateMessage('${player.name}')" data-bs-toggle="tooltip" title="Send private message">
                                    <i class="fas fa-comment"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        cardBody.innerHTML = `<div class="row g-3">${playersHtml}</div>`;

        // Re-initialize tooltips for the new elements
        const tooltipTriggerList = [].slice.call(cardBody.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

function refreshBanlist() {
    const btn = event.target.closest('button');
    const icon = btn.querySelector('i');
    icon.classList.add('fa-spin');

    fetch('/api/banlist')
        .then(response => response.json())
        .then(data => {
            ToastManager.show('Ban list refreshed!', 'success');
            // Refresh the page to show updated ban list
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        })
        .catch(error => {
            ToastManager.show('Failed to refresh ban list', 'error');
        })
        .finally(() => {
            icon.classList.remove('fa-spin');
        });
}

function refreshWhitelist() {
    const btn = event.target.closest('button');
    const icon = btn.querySelector('i');
    icon.classList.add('fa-spin');

    fetch('/api/whitelist')
        .then(response => response.json())
        .then(data => {
            ToastManager.show('Whitelist refreshed!', 'success');
            // Refresh the page to show updated whitelist
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        })
        .catch(error => {
            ToastManager.show('Failed to refresh whitelist', 'error');
        })
        .finally(() => {
            icon.classList.remove('fa-spin');
        });
}

function confirmRemoveWhitelist(playerName) {
    Swal.fire({
        title: 'Remove from Whitelist?',
        text: `Are you sure you want to remove ${playerName} from the whitelist?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, remove!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/action/whitelist/remove';

            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'player_name';
            input.value = playerName;

            form.appendChild(input);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function showBanInfo(playerName) {
    Swal.fire({
        title: `Ban Information`,
        html: `
            <div class="text-start">
                <p><strong>Player:</strong> ${playerName}</p>
                <p><strong>Status:</strong> <span class="badge bg-danger">Banned</span></p>
                <p><strong>Action:</strong> Use the unban button to remove this player from the ban list</p>
            </div>
        `,
        icon: 'info',
        confirmButtonText: 'Close',
        confirmButtonColor: '#6366f1',
        customClass: {
            popup: 'text-start'
        }
    });
}

function sendPrivateMessage(playerName) {
    Swal.fire({
        title: `Send Message to ${playerName}`,
        input: 'text',
        inputPlaceholder: 'Enter your message...',
        showCancelButton: true,
        confirmButtonText: 'Send',
        confirmButtonColor: '#6366f1',
        cancelButtonColor: '#6b7280',
        inputValidator: (value) => {
            if (!value) {
                return 'Please enter a message!';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Here you would send the message via API
            ToastManager.show(`Message sent to ${playerName}!`, 'success');
        }
    });
}

// Legacy function - kept for backward compatibility
function confirmAction(message, action, playerName) {
    console.log('Legacy confirmAction called - consider updating to specific functions');
    Swal.fire({
        title: 'Are you sure?',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, proceed!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            ToastManager.show('Action completed!', 'success');
        }
    });
}

// Handle modal forms with AJAX - Non-blocking modals
document.addEventListener('DOMContentLoaded', function() {

    // Initialize modals manually to avoid backdrop issues
    document.querySelectorAll('.modal').forEach(modalElement => {
        try {
            // Initialize modal with explicit configuration
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: true,
                keyboard: true,
                focus: true
            });
            console.log('Modal initialized successfully:', modalElement.id);
        } catch (error) {
            console.error('Error initializing modal:', modalElement.id, error);
        }
    });

    // Handle unban modal show event
    const unbanModal = document.getElementById('unbanModal');
    if (unbanModal) {
        unbanModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget; // Button that triggered the modal
            const playerName = button.getAttribute('data-player-name');
            console.log('Modal show event - setting player name:', playerName);

            const playerNameInput = document.getElementById('unbanPlayerName');
            if (playerNameInput && playerName) {
                playerNameInput.value = playerName;
                console.log('Successfully set player name in modal:', playerName);
            } else {
                console.error('Failed to set player name - input:', playerNameInput, 'name:', playerName);
            }
        });
    }

    // Function to properly close modal
    function closeModal(modal) {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
            modalInstance.hide();
        }
    }

    // Handle all modal forms
    document.querySelectorAll('.modal-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            const successMessage = this.getAttribute('data-success-message');
            const modal = this.closest('.modal');

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;

            // Create FormData from the form and convert to URL-encoded string
            const formData = new FormData(this);
            const urlEncodedData = new URLSearchParams();

            // Debug: Log form data
            console.log('Form action:', this.action);
            console.log('Form data:');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
                urlEncodedData.append(key, value);
            }
            console.log('URL encoded data:', urlEncodedData.toString());

            // Submit via AJAX
            fetch(this.action, {
                method: 'POST',
                body: urlEncodedData,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers.get('content-type'));

                if (response.ok) {
                    // Try to parse JSON response
                    return response.json().catch(err => {
                        console.error('Failed to parse JSON:', err);
                        // If not JSON, assume success (redirect response)
                        return { success: true };
                    });
                } else {
                    // Handle error responses
                    return response.text().then(text => {
                        console.error('Error response text:', text);
                        try {
                            const errorData = JSON.parse(text);
                            throw new Error(errorData.error || `Server error: ${response.status}`);
                        } catch (parseError) {
                            throw new Error(`Server error: ${response.status} ${response.statusText}`);
                        }
                    });
                }
            })
            .then(data => {
                // Close modal properly
                closeModal(modal);

                // Show success message
                if (data.message) {
                    ToastManager.show(data.message, 'success');
                } else if (successMessage) {
                    ToastManager.show(successMessage, 'success');
                }

                // Refresh the page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch(error => {
                console.error('Fetch error:', error);
                console.error('Error stack:', error.stack);
                const errorMessage = error.message || 'An unexpected error occurred. Please try again.';
                console.error('Showing error message:', errorMessage);

                // Fallback error display
                try {
                    ToastManager.show(errorMessage, 'error');
                } catch (toastError) {
                    console.error('ToastManager error:', toastError);
                    alert(errorMessage); // Fallback to alert
                }

                // Restore button state on error
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    });

    // Add escape key functionality to close modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal);
            }
        }
    });
});

// Auto-refresh player data every 30 seconds
setInterval(() => {
    if (window.location.pathname === '/players') {
        fetch('/api/players/detailed')
            .then(response => response.json())
            .then(data => {
                const badge = document.querySelector('.badge');
                if (badge && data.online_count !== undefined) {
                    const currentCount = parseInt(badge.textContent.split('/')[0]);
                    if (currentCount !== data.online_count) {
                        badge.textContent = `${data.online_count}/${data.max_players}`;
                        badge.classList.add('animate__animated', 'animate__pulse');
                        setTimeout(() => {
                            badge.classList.remove('animate__animated', 'animate__pulse');
                        }, 1000);

                        // Update the full player list with detailed data
                        updatePlayerList(data.detailed_players || []);
                    }
                }
            })
            .catch(error => console.log('Auto-refresh error:', error));
    }
}, 30000);
</script>
{% endblock %}
