#!/usr/bin/env python3
"""
Minecraft Server Manager
A Python interface to view and manage players on your Minecraft server via RCON.
"""

import subprocess
import json
import re
import time
from datetime import datetime
from typing import List, Dict, Optional
import argparse


class MinecraftServerManager:
    def __init__(self, compose_path: str = "."):
        """Initialize the Minecraft Server Manager."""
        self.compose_path = compose_path
        self.service_name = "mc"
    
    def _run_rcon_command(self, command: str) -> str:
        """Execute an RCON command and return the result."""
        try:
            cmd = [
                "docker-compose", "-f", f"{self.compose_path}/docker-compose.yml",
                "exec", "-T", self.service_name, "rcon-cli", command
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return f"Error: {result.stderr.strip()}"
        except subprocess.TimeoutExpired:
            return "Error: Command timed out"
        except Exception as e:
            return f"Error: {str(e)}"
    
    def _get_server_logs(self, lines: int = 50) -> str:
        """Get recent server logs."""
        try:
            cmd = [
                "docker-compose", "-f", f"{self.compose_path}/docker-compose.yml",
                "logs", "--tail", str(lines), self.service_name
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            return result.stdout if result.returncode == 0 else result.stderr
        except Exception as e:
            return f"Error getting logs: {str(e)}"
    
    def get_online_players(self) -> Dict:
        """Get list of currently online players."""
        result = self._run_rcon_command("list")
        
        # Parse the result: "There are X of a max of Y players online: player1, player2"
        players_info = {
            "online_count": 0,
            "max_players": 0,
            "players": []
        }
        
        if "There are" in result:
            match = re.search(r"There are (\d+) of a max of (\d+) players online:?(.*)", result)
            if match:
                players_info["online_count"] = int(match.group(1))
                players_info["max_players"] = int(match.group(2))
                if match.group(3).strip():
                    players_info["players"] = [p.strip() for p in match.group(3).split(",") if p.strip()]
        
        return players_info
    
    def get_player_info(self, player_name: str) -> Dict:
        """Get detailed information about a specific player."""
        info = {"name": player_name, "online": False}
        
        # Check if player is online
        online_players = self.get_online_players()
        info["online"] = player_name in online_players["players"]
        
        if info["online"]:
            # Get player position
            pos_result = self._run_rcon_command(f"data get entity {player_name} Pos")
            if "has the following entity data:" in pos_result:
                try:
                    pos_data = pos_result.split(":")[-1].strip()
                    # Parse coordinates like [x, y, z]
                    coords = re.findall(r'-?\d+\.?\d*', pos_data)
                    if len(coords) >= 3:
                        info["position"] = {
                            "x": float(coords[0]),
                            "y": float(coords[1]),
                            "z": float(coords[2])
                        }
                except:
                    pass
            
            # Get player health
            health_result = self._run_rcon_command(f"data get entity {player_name} Health")
            if "has the following entity data:" in health_result:
                try:
                    health_data = health_result.split(":")[-1].strip()
                    health_match = re.search(r'(\d+\.?\d*)', health_data)
                    if health_match:
                        info["health"] = float(health_match.group(1))
                except:
                    pass
            
            # Get player game mode
            gamemode_result = self._run_rcon_command(f"data get entity {player_name} playerGameType")
            if "has the following entity data:" in gamemode_result:
                try:
                    gamemode_data = gamemode_result.split(":")[-1].strip()
                    info["gamemode"] = int(gamemode_data)
                except:
                    pass
        
        return info
    
    def get_server_status(self) -> Dict:
        """Get overall server status."""
        players = self.get_online_players()
        
        # Get server performance info
        tps_result = self._run_rcon_command("forge tps")  # Works on some servers
        if "Error" in tps_result:
            tps_result = "TPS info not available"
        
        return {
            "timestamp": datetime.now().isoformat(),
            "players": players,
            "tps": tps_result,
            "status": "online" if players["online_count"] >= 0 else "offline"
        }
    
    def kick_player(self, player_name: str, reason: str = "Kicked by admin") -> str:
        """Kick a player from the server."""
        return self._run_rcon_command(f"kick {player_name} {reason}")
    
    def ban_player(self, player_name: str, reason: str = "Banned by admin") -> str:
        """Ban a player from the server."""
        return self._run_rcon_command(f"ban {player_name} {reason}")
    
    def unban_player(self, player_name: str) -> str:
        """Unban a player."""
        return self._run_rcon_command(f"pardon {player_name}")
    
    def op_player(self, player_name: str) -> str:
        """Give operator status to a player."""
        return self._run_rcon_command(f"op {player_name}")
    
    def deop_player(self, player_name: str) -> str:
        """Remove operator status from a player."""
        return self._run_rcon_command(f"deop {player_name}")
    
    def teleport_player(self, player_name: str, target: str) -> str:
        """Teleport a player to another player or coordinates."""
        return self._run_rcon_command(f"tp {player_name} {target}")
    
    def send_message(self, message: str) -> str:
        """Send a message to all players."""
        return self._run_rcon_command(f"say {message}")
    
    def send_private_message(self, player_name: str, message: str) -> str:
        """Send a private message to a specific player."""
        return self._run_rcon_command(f"msg {player_name} {message}")
    
    def get_whitelist(self) -> List[str]:
        """Get the whitelist."""
        result = self._run_rcon_command("whitelist list")
        if "There are no whitelisted players" in result:
            return []
        # Parse whitelist result
        if "whitelisted players:" in result:
            players_part = result.split("whitelisted players:")[-1].strip()
            return [p.strip() for p in players_part.split(",") if p.strip()]
        return []
    
    def get_banlist(self) -> List[str]:
        """Get the ban list."""
        result = self._run_rcon_command("banlist players")

        # Handle different possible responses
        if "There are no banned players" in result or "There are 0 ban(s)" in result:
            return []

        # Parse banlist result - format: "There are X ban(s):PlayerName was banned by..."
        if "There are" in result and "ban(s):" in result:
            # Split by "ban(s):" and get the part after it
            ban_part = result.split("ban(s):")[-1].strip()

            # Extract player names - they appear before " was banned by"
            banned_players = []
            # Split by common separators and look for player names
            for line in ban_part.split('\n'):
                if " was banned by" in line:
                    # Extract player name (everything before " was banned by")
                    player_name = line.split(" was banned by")[0].strip()
                    if player_name:
                        banned_players.append(player_name)

            return banned_players

        # Fallback: try the old format for compatibility
        if "banned players:" in result:
            players_part = result.split("banned players:")[-1].strip()
            return [p.strip() for p in players_part.split(",") if p.strip()]

        return []

    def add_to_whitelist(self, player_name: str) -> str:
        """Add a player to the whitelist."""
        return self._run_rcon_command(f"whitelist add {player_name}")

    def remove_from_whitelist(self, player_name: str) -> str:
        """Remove a player from the whitelist."""
        return self._run_rcon_command(f"whitelist remove {player_name}")
    
    def get_recent_activity(self, lines: int = 20) -> List[Dict]:
        """Get recent player activity from logs."""
        logs = self._get_server_logs(lines)
        activities = []
        
        for line in logs.split('\n'):
            # Look for join/leave events
            if 'joined the game' in line:
                match = re.search(r'\[(\d{2}:\d{2}:\d{2})\].*?(\w+) joined the game', line)
                if match:
                    activities.append({
                        "time": match.group(1),
                        "player": match.group(2),
                        "action": "joined"
                    })
            elif 'left the game' in line:
                match = re.search(r'\[(\d{2}:\d{2}:\d{2})\].*?(\w+) left the game', line)
                if match:
                    activities.append({
                        "time": match.group(1),
                        "player": match.group(2),
                        "action": "left"
                    })
        
        return activities[-10:]  # Return last 10 activities


def main():
    """Command line interface for the Minecraft Server Manager."""
    parser = argparse.ArgumentParser(description="Minecraft Server Manager")
    parser.add_argument("--path", default=".", help="Path to docker-compose.yml directory")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Status command
    subparsers.add_parser("status", help="Show server status")
    
    # Players command
    players_parser = subparsers.add_parser("players", help="Show online players")
    players_parser.add_argument("--detailed", action="store_true", help="Show detailed player info")
    
    # Player info command
    info_parser = subparsers.add_parser("info", help="Show detailed player information")
    info_parser.add_argument("player", help="Player name")
    
    # Kick command
    kick_parser = subparsers.add_parser("kick", help="Kick a player")
    kick_parser.add_argument("player", help="Player name")
    kick_parser.add_argument("--reason", default="Kicked by admin", help="Kick reason")
    
    # Ban command
    ban_parser = subparsers.add_parser("ban", help="Ban a player")
    ban_parser.add_argument("player", help="Player name")
    ban_parser.add_argument("--reason", default="Banned by admin", help="Ban reason")
    
    # Unban command
    unban_parser = subparsers.add_parser("unban", help="Unban a player")
    unban_parser.add_argument("player", help="Player name")
    
    # Op command
    op_parser = subparsers.add_parser("op", help="Give operator status")
    op_parser.add_argument("player", help="Player name")
    
    # Deop command
    deop_parser = subparsers.add_parser("deop", help="Remove operator status")
    deop_parser.add_argument("player", help="Player name")
    
    # Message command
    msg_parser = subparsers.add_parser("say", help="Send message to all players")
    msg_parser.add_argument("message", help="Message to send")
    
    # Activity command
    subparsers.add_parser("activity", help="Show recent player activity")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = MinecraftServerManager(args.path)
    
    try:
        if args.command == "status":
            status = manager.get_server_status()
            print(f"Server Status: {status['status']}")
            print(f"Players Online: {status['players']['online_count']}/{status['players']['max_players']}")
            if status['players']['players']:
                print(f"Online Players: {', '.join(status['players']['players'])}")
            print(f"Timestamp: {status['timestamp']}")
        
        elif args.command == "players":
            players = manager.get_online_players()
            print(f"Players Online: {players['online_count']}/{players['max_players']}")
            if players['players']:
                if args.detailed:
                    for player in players['players']:
                        info = manager.get_player_info(player)
                        print(f"\n{player}:")
                        if 'position' in info:
                            pos = info['position']
                            print(f"  Position: {pos['x']:.1f}, {pos['y']:.1f}, {pos['z']:.1f}")
                        if 'health' in info:
                            print(f"  Health: {info['health']}")
                        if 'gamemode' in info:
                            modes = {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}
                            print(f"  Game Mode: {modes.get(info['gamemode'], 'Unknown')}")
                else:
                    print(f"Online: {', '.join(players['players'])}")
            else:
                print("No players online")
        
        elif args.command == "info":
            info = manager.get_player_info(args.player)
            print(f"Player: {info['name']}")
            print(f"Online: {info['online']}")
            if info['online']:
                if 'position' in info:
                    pos = info['position']
                    print(f"Position: {pos['x']:.1f}, {pos['y']:.1f}, {pos['z']:.1f}")
                if 'health' in info:
                    print(f"Health: {info['health']}")
                if 'gamemode' in info:
                    modes = {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}
                    print(f"Game Mode: {modes.get(info['gamemode'], 'Unknown')}")
        
        elif args.command == "kick":
            result = manager.kick_player(args.player, args.reason)
            print(result)
        
        elif args.command == "ban":
            result = manager.ban_player(args.player, args.reason)
            print(result)
        
        elif args.command == "unban":
            result = manager.unban_player(args.player)
            print(result)
        
        elif args.command == "op":
            result = manager.op_player(args.player)
            print(result)
        
        elif args.command == "deop":
            result = manager.deop_player(args.player)
            print(result)
        
        elif args.command == "say":
            result = manager.send_message(args.message)
            print(result)
        
        elif args.command == "activity":
            activities = manager.get_recent_activity()
            if activities:
                print("Recent Activity:")
                for activity in activities:
                    print(f"  {activity['time']} - {activity['player']} {activity['action']}")
            else:
                print("No recent activity found")
    
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
